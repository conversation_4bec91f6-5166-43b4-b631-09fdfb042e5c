import threading
import time
import re
import redis  # Import redis module directly
from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.utils.enhanced_logger import get_logger
from app.config.config import settings
import datetime

logger = get_logger("RedisEventListener")

# Global RedisEventListener instance (singleton)
_redis_event_listener_instance = None


def get_redis_event_listener(workflow_state_manager=None):
    """
    Get or create the global RedisEventListener instance (singleton pattern).

    Args:
        workflow_state_manager: Optional reference to a WorkflowStateManager instance.
            If provided and the listener already exists, it will update the reference.

    Returns:
        RedisEventListener: The global RedisEventListener instance.
    """
    global _redis_event_listener_instance
    if _redis_event_listener_instance is None:
        logger.info("Creating new RedisEventListener instance")
        _redis_event_listener_instance = RedisEventListener(workflow_state_manager)
        _redis_event_listener_instance.start()
    elif (
        workflow_state_manager
        and _redis_event_listener_instance.workflow_state_manager
        != workflow_state_manager
    ):
        # Update the workflow state manager reference if needed
        logger.info(
            "Updating workflow state manager reference in existing RedisEventListener"
        )
        _redis_event_listener_instance.set_workflow_state_manager(
            workflow_state_manager
        )
    return _redis_event_listener_instance


class RedisEventListener:
    """
    Listens for Redis keyspace events, particularly key deletions,
    and archives the data to PostgreSQL before it's permanently lost.
    """

    def __init__(self, workflow_state_manager=None):
        """
        Initialize the Redis event listener.

        Args:
            workflow_state_manager: Optional reference to a WorkflowStateManager instance.
                If provided, will use its methods to archive data.
        """
        self.logger = logger
        self.workflow_state_manager = workflow_state_manager
        self.running = False
        self.thread = None

        # Keep-alive settings
        self.keep_alive_interval = 60  # Send keep-alive every 60 seconds
        self.last_keep_alive_time = datetime.datetime.now()
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_backoff = 5  # Initial backoff in seconds

        # Initialize Redis managers
        self.results_redis_manager = RedisManager(
            db_index=(
                int(settings.redis_results_db_index)
                if settings.redis_results_db_index
                else 0
            )
        )
        self.state_redis_manager = RedisManager(
            db_index=(
                int(settings.redis_state_db_index)
                if settings.redis_state_db_index
                else 1
            )
        )

        # Initialize PostgreSQL manager using the singleton pattern
        self.postgres_manager = get_postgres_manager()

        # Patterns for extracting IDs from Redis keys
        self.result_key_pattern = re.compile(r"result:(.+)")
        self.state_key_pattern = re.compile(r"workflow_state:(.+)")

    def _configure_redis_notifications(self):
        """
        Configure Redis to publish keyspace events.
        """
        try:
            # Configure Redis to publish keyspace events for key deletions and expirations
            # KEx = Keyspace events (K) for all commands (E) and expired events (x)
            if self.results_redis_manager.is_connected():
                self.results_redis_manager.redis_client.config_set(
                    "notify-keyspace-events", "KEx"
                )
                self.logger.info(
                    "Configured Redis results DB for keyspace notifications including expirations"
                )

            if self.state_redis_manager.is_connected():
                self.state_redis_manager.redis_client.config_set(
                    "notify-keyspace-events", "KEx"
                )
                self.logger.info(
                    "Configured Redis state DB for keyspace notifications including expirations"
                )

            return True
        except Exception as e:
            self.logger.error(
                f"Error configuring Redis for keyspace notifications: {e}"
            )
            return False

    def _listen_for_events(self):
        """
        Listen for Redis keyspace events in a separate thread.
        """
        self.logger.info("Starting Redis event listener thread")

        # Configure Redis for notifications
        if not self._configure_redis_notifications():
            self.logger.error(
                "Failed to configure Redis for notifications. Event listener not started."
            )
            return

        # Create a new Redis connection for pub/sub
        # Note: For pubsub, we need to be careful with decode_responses
        # Create a separate connection with decode_responses=False for pubsub
        try:
            # Get connection parameters from the existing client
            results_params = (
                self.results_redis_manager.redis_client.connection_pool.connection_kwargs.copy()
            )
            state_params = (
                self.state_redis_manager.redis_client.connection_pool.connection_kwargs.copy()
            )

            # Set decode_responses to False for pubsub to avoid decoding issues
            results_params["decode_responses"] = False
            state_params["decode_responses"] = False

            # Create new Redis clients for pubsub with these parameters
            results_pubsub_client = redis.Redis(**results_params).pubsub()
            state_pubsub_client = redis.Redis(**state_params).pubsub()

            self.logger.info(
                "Created dedicated Redis clients for pubsub with decode_responses=False"
            )

            results_pubsub = results_pubsub_client
            state_pubsub = state_pubsub_client
        except Exception as e:
            self.logger.warning(
                f"Failed to create dedicated pubsub clients: {e}. Using default clients."
            )
            results_pubsub = self.results_redis_manager.redis_client.pubsub()
            state_pubsub = self.state_redis_manager.redis_client.pubsub()

        # Subscribe to keyspace events for key deletions
        results_db = self.results_redis_manager.db_index
        state_db = self.state_redis_manager.db_index

        # Subscribe to all key events in both databases
        results_pubsub.psubscribe(f"__keyspace@{results_db}__:*")
        state_pubsub.psubscribe(f"__keyspace@{state_db}__:*")

        # Log Redis client configuration
        self.logger.info(
            f"Redis results client decode_responses: {self.results_redis_manager.redis_client.connection_pool.connection_kwargs.get('decode_responses', False)}"
        )
        self.logger.info(
            f"Redis state client decode_responses: {self.state_redis_manager.redis_client.connection_pool.connection_kwargs.get('decode_responses', False)}"
        )

        self.logger.info(
            f"Subscribed to keyspace events for Redis DB {results_db} and {state_db}"
        )

        # Store channel patterns for potential reconnection
        results_channel_pattern = f"__keyspace@{results_db}__:*"
        state_channel_pattern = f"__keyspace@{state_db}__:*"

        while self.running:
            try:
                # Check if it's time to send a keep-alive
                current_time = datetime.datetime.now()
                time_since_last_keep_alive = (
                    current_time - self.last_keep_alive_time
                ).total_seconds()

                if time_since_last_keep_alive >= self.keep_alive_interval:
                    self.logger.debug("Sending keep-alive to Redis connections")

                    # Send keep-alive to both connections
                    results_alive = self._send_keep_alive(results_pubsub)
                    state_alive = self._send_keep_alive(state_pubsub)

                    # Update the last keep-alive time
                    self.last_keep_alive_time = current_time

                    # Log the keep-alive status
                    self.logger.debug(
                        f"Keep-alive status: results={results_alive}, state={state_alive}"
                    )

                # Check for messages from results DB with error handling
                try:
                    results_message = results_pubsub.get_message()
                    if results_message and results_message["type"] == "pmessage":
                        self._handle_results_db_event(results_message)
                except redis.exceptions.ConnectionError as e:
                    self.logger.warning(
                        f"Connection error when getting results message: {e}"
                    )
                    # Try to reconnect
                    results_pubsub, success = self._check_and_reconnect_pubsub(
                        results_pubsub, results_db, results_channel_pattern
                    )
                    if not success:
                        self.logger.error(
                            "Failed to reconnect to results Redis, will retry later"
                        )

                # Check for messages from state DB with error handling
                try:
                    state_message = state_pubsub.get_message()
                    if state_message and state_message["type"] == "pmessage":
                        self._handle_state_db_event(state_message)
                except redis.exceptions.ConnectionError as e:
                    self.logger.warning(
                        f"Connection error when getting state message: {e}"
                    )
                    # Try to reconnect
                    state_pubsub, success = self._check_and_reconnect_pubsub(
                        state_pubsub, state_db, state_channel_pattern
                    )
                    if not success:
                        self.logger.error(
                            "Failed to reconnect to state Redis, will retry later"
                        )

                # Small sleep to prevent CPU hogging
                time.sleep(0.01)

            except Exception as e:
                self.logger.error(
                    f"Unexpected error in Redis event listener loop: {e}", exc_info=True
                )
                # Continue the loop to maintain the listener active
                time.sleep(1)  # Longer sleep on error to avoid rapid error loops

        # Clean up
        results_pubsub.unsubscribe()
        state_pubsub.unsubscribe()
        self.logger.info("Redis event listener thread stopped")

    def _handle_results_db_event(self, message):
        """
        Handle events from the results Redis database.

        Args:
            message: The Redis pub/sub message.
        """
        try:
            # Log the raw message for debugging
            self.logger.debug(f"Raw results DB event message: {message}")

            # Extract the key from the channel - handle both bytes and str
            channel = message["channel"]
            self.logger.debug(f"Channel type: {type(channel)}, value: {channel}")

            if isinstance(channel, bytes):
                channel = channel.decode("utf-8")
                self.logger.debug(f"Decoded channel: {channel}")

            # Extract the full key including the prefix
            # Format: __keyspace@{db}__:result:{transition_id}
            parts = channel.split(":")
            if len(parts) < 2:
                self.logger.warning(f"Invalid channel format: {channel}")
                return

            # Reconstruct the full key (result:{transition_id})
            key = ":".join(parts[1:])
            self.logger.debug(f"Extracted key: {key}")

            # Handle data - handle both bytes and str
            event = message["data"]
            self.logger.debug(f"Event data type: {type(event)}, value: {event}")

            if isinstance(event, bytes):
                event = event.decode("utf-8")
                self.logger.debug(f"Decoded event: {event}")

            # Process 'del' and 'expired' events for result keys
            if (event == "del" or event == "expired") and key.startswith("result:"):
                match = self.result_key_pattern.match(key)
                if match:
                    transition_id = match.group(1)
                    self.logger.info(
                        f"Detected {event} event for result of transition: {transition_id}"
                    )

                    # If we have a workflow state manager, use it to archive the result
                    if self.workflow_state_manager:
                        self.logger.info(
                            f"Archiving result for transition: {transition_id}"
                        )
                        try:
                            self.workflow_state_manager.archive_transition_result(
                                transition_id
                            )
                        except Exception as archive_e:
                            self.logger.error(
                                f"Error archiving result for transition {transition_id}: {archive_e}",
                                exc_info=True
                            )
                    else:
                        self.logger.warning(
                            f"No workflow state manager available to archive result for transition: {transition_id}"
                        )
        except Exception as e:
            self.logger.error(f"Error handling results DB event: {e}", exc_info=True)

    def _handle_state_db_event(self, message):
        """
        Handle events from the state Redis database.

        Args:
            message: The Redis pub/sub message.
        """
        try:
            # Log the raw message for debugging
            self.logger.debug(f"Raw state DB event message: {message}")

            # Extract the key from the channel - handle both bytes and str
            channel = message["channel"]
            self.logger.debug(f"Channel type: {type(channel)}, value: {channel}")

            if isinstance(channel, bytes):
                channel = channel.decode("utf-8")
                self.logger.debug(f"Decoded channel: {channel}")

            # Extract the full key including the prefix
            # Format: __keyspace@{db}__:workflow_state:{workflow_id}
            parts = channel.split(":")
            if len(parts) < 2:
                self.logger.warning(f"Invalid channel format: {channel}")
                return

            # Reconstruct the full key (workflow_state:{workflow_id})
            key = ":".join(parts[1:])
            self.logger.debug(f"Extracted key: {key}")

            # Handle data - handle both bytes and str
            event = message["data"]
            self.logger.debug(f"Event data type: {type(event)}, value: {event}")

            if isinstance(event, bytes):
                event = event.decode("utf-8")
                self.logger.debug(f"Decoded event: {event}")

            # Process 'del' and 'expired' events for workflow state keys
            if (event == "del" or event == "expired") and key.startswith(
                "workflow_state:"
            ):
                match = self.state_key_pattern.match(key)
                if match:
                    workflow_id = match.group(1)
                    self.logger.info(
                        f"Detected {event} event for workflow state of workflow: {workflow_id}"
                    )

                    # If we have a workflow state manager, use it to archive the state
                    if self.workflow_state_manager:
                        self.logger.info(
                            f"Archiving workflow state for workflow: {workflow_id}"
                        )
                        self.workflow_state_manager.archive_workflow_state()
                    else:
                        self.logger.warning(
                            f"No workflow state manager available to archive state for workflow: {workflow_id}"
                        )
        except Exception as e:
            self.logger.error(f"Error handling state DB event: {e}", exc_info=True)

    def start(self):
        """
        Start the Redis event listener in a separate thread.
        """
        if self.running:
            self.logger.warning("Redis event listener is already running")
            return

        self.running = True
        self.thread = threading.Thread(target=self._listen_for_events)
        self.thread.daemon = True  # Thread will exit when main program exits
        self.thread.start()
        self.logger.info("Redis event listener started")

    def stop(self):
        """
        Stop the Redis event listener thread.
        """
        if not self.running:
            self.logger.warning("Redis event listener is not running")
            return

        self.running = False
        if self.thread:
            self.thread.join(timeout=2.0)  # Wait for thread to finish
            self.logger.info("Redis event listener stopped")

    def set_workflow_state_manager(self, workflow_state_manager):
        """
        Set the workflow state manager reference.

        Args:
            workflow_state_manager: A WorkflowStateManager instance.
        """
        self.workflow_state_manager = workflow_state_manager
        self.logger.info("Workflow state manager reference updated")

    def _send_keep_alive(self, pubsub_client):
        """
        Send a keep-alive ping to the Redis server to prevent connection timeout.

        Args:
            pubsub_client: The Redis PubSub client to ping.

        Returns:
            bool: True if ping was successful, False otherwise.
        """
        try:
            # Get the underlying Redis client from the pubsub object
            redis_client = pubsub_client.connection_pool.connection_kwargs
            host = redis_client.get("host", "unknown")
            port = redis_client.get("port", "unknown")

            # Send a PING command - for PubSub clients we need to use the underlying connection
            pubsub_client.connection.send_command("PING")
            response = pubsub_client.connection.read_response()

            if response:
                self.logger.debug(
                    f"Sent keep-alive PING to Redis server at {host}:{port}, response: {response}"
                )
                return True
            else:
                self.logger.warning(
                    f"No response from Redis server at {host}:{port} to keep-alive PING"
                )
                return False
        except Exception as e:
            self.logger.warning(f"Failed to send keep-alive PING to Redis: {e}")
            return False

    def _check_and_reconnect_pubsub(self, pubsub_client, db_index, channel_pattern):
        """
        Check if the PubSub connection is alive and reconnect if necessary.

        Args:
            pubsub_client: The Redis PubSub client to check.
            db_index: The Redis database index.
            channel_pattern: The channel pattern to resubscribe to.

        Returns:
            tuple: (reconnected_client, success_flag)
        """
        try:
            # Try to ping the connection
            if self._send_keep_alive(pubsub_client):
                # Connection is alive, reset reconnect attempts
                self.reconnect_attempts = 0
                return pubsub_client, True

            # If we get here, the connection is not responding but didn't raise an exception
            self.logger.warning(
                "Redis PubSub connection is not responding, attempting to reconnect"
            )

        except redis.exceptions.ConnectionError as e:
            self.logger.warning(f"Redis connection error: {e}")
        except Exception as e:
            self.logger.warning(f"Unexpected error with Redis connection: {e}")

        # If we get here, we need to reconnect
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.logger.error(
                f"Maximum reconnection attempts ({self.max_reconnect_attempts}) reached"
            )
            return pubsub_client, False

        # Calculate backoff with exponential increase
        backoff = self.reconnect_backoff * (2**self.reconnect_attempts)
        self.logger.info(
            f"Waiting {backoff} seconds before reconnection attempt {self.reconnect_attempts + 1}/{self.max_reconnect_attempts}"
        )
        time.sleep(backoff)

        # Attempt to reconnect
        try:
            self.logger.info(f"Attempting to reconnect to Redis DB {db_index}")

            # Get connection parameters
            if db_index == self.results_redis_manager.db_index:
                params = (
                    self.results_redis_manager.redis_client.connection_pool.connection_kwargs.copy()
                )
            else:
                params = (
                    self.state_redis_manager.redis_client.connection_pool.connection_kwargs.copy()
                )

            # Set decode_responses to False for pubsub
            params["decode_responses"] = False

            # Create new Redis client and pubsub
            new_client = redis.Redis(**params).pubsub()

            # Resubscribe to the channel pattern
            new_client.psubscribe(channel_pattern)

            self.logger.info(
                f"Successfully reconnected to Redis DB {db_index} and resubscribed to {channel_pattern}"
            )

            # Reset reconnect attempts on success
            self.reconnect_attempts = 0
            return new_client, True

        except Exception as e:
            self.reconnect_attempts += 1
            self.logger.error(
                f"Failed to reconnect to Redis (attempt {self.reconnect_attempts}/{self.max_reconnect_attempts}): {e}"
            )
            return pubsub_client, False
