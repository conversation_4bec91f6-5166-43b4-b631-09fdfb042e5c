/**
 * Utility functions for field handling in the workflow builder
 */

/**
 * Determines whether a field should be included in the execution dialog or start node data
 * based on its configuration and connection status.
 *
 * @param isRequired Whether the field is required
 * @param isDirectlyConnected Whether the field is directly connected to the Start node
 * @param hasConfiguredValue Whether the field already has a value configured in the inspector panel
 * @param hasIncomingConnection Whether the field has an incoming connection from another node
 * @returns Boolean indicating whether the field should be included
 */
export function shouldIncludeField(
  isRequired: boolean,
  isDirectlyConnected: boolean,
  hasConfiguredValue: boolean,
  hasIncomingConnection: boolean = false
): boolean {
  // Fields directly connected to the Start node should ALWAYS be included,
  // regardless of whether they have pre-configured values or incoming connections
  if (isDirectlyConnected) {
    return true;
  }

  // Fields with incoming connections from other nodes should be excluded
  // (unless they're directly connected to the Start node, which we already checked)
  if (hasIncomingConnection) {
    return false;
  }

  // For required fields, only include them if they don't have a configured value
  return isRequired && !hasConfiguredValue;
}

/**
 * Logs detailed information about a field's status for debugging purposes
 *
 * @param context The context where this function is called (e.g., 'ExecutionDialog', 'ExtractStartNodeData')
 * @param fieldId The ID of the field
 * @param nodeName The name of the node containing the field
 * @param fieldName The name of the field
 * @param isRequired Whether the field is required
 * @param requiredValue The actual value of the required property
 * @param isDirectlyConnected Whether the field is directly connected to the Start node
 * @param hasConfiguredValue Whether the field already has a value configured in the inspector panel
 * @param configuredValue The actual configured value, if any
 * @param hasIncomingConnection Whether the field has an incoming connection from another node
 */
export function logFieldStatus(
  context: string,
  fieldId: string,
  nodeName: string,
  fieldName: string,
  isRequired: boolean,
  requiredValue: any,
  isDirectlyConnected: boolean,
  hasConfiguredValue: boolean,
  configuredValue?: any,
  hasIncomingConnection: boolean = false
): void {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  const shouldInclude = shouldIncludeField(isRequired, isDirectlyConnected, hasConfiguredValue, hasIncomingConnection);

  console.log(`[${timestamp}] [${context}] Field ${nodeName}.${fieldName} status check:
    - required property value: ${requiredValue === undefined ? "undefined" : requiredValue}
    - required !== false: ${isRequired ? "YES" : "NO"}
    - directly connected to Start: ${isDirectlyConnected ? "YES" : "NO"}
    - has configured value: ${hasConfiguredValue ? "YES" : "NO"}
    - has incoming connection: ${hasIncomingConnection ? "YES" : "NO"}
    - configured value: ${hasConfiguredValue ? JSON.stringify(configuredValue) : "undefined"}
    - Should include: ${shouldInclude ? "YES" : "NO"}`);
}
