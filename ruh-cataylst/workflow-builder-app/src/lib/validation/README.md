# Frontend Validation System

This directory contains a pure TypeScript implementation of the workflow validation system, designed to replace the backend validation API calls with client-side validation.

## Key Features

- **Pure Frontend Implementation**: All validation happens client-side without any API calls
- **Comprehensive Validation Rules**: Validates nodes, edges, connectivity, and field values
- **Configurable Validation**: Different validation rules for saving, executing, and editing
- **Feature Flags**: Control validation behavior with feature flags
- **Backend Compatibility**: Maintains compatibility with code expecting backend validation format

## Directory Structure

- `types.ts`: TypeScript interfaces and enums for validation
- `errors.ts`: Error creation and handling utilities
- `utils.ts`: Utility functions for validation
- `nodeValidation.ts`: Node structure validation
- `edgeValidation.ts`: Edge structure validation
- `connectivityValidation.ts`: Workflow connectivity validation
- `fieldValidation.ts`: Field value validation
- `workflowValidation.ts`: Main workflow validation logic
- `smartValidation.ts`: Smart validation functions with feature flag support
- `frontendValidationAdapter.ts`: Adapter for backend API compatibility
- `__tests__/`: Test files for all validation components

## Usage

### Basic Validation

```typescript
import { validateWorkflow } from "@/lib/validation";

// Validate a workflow
const result = validateWorkflow(nodes, edges);

if (result.isValid) {
  console.log("Workflow is valid!");
} else {
  console.error("Validation errors:", result.errors);
}
```

### Smart Validation

```typescript
import { validateWorkflowBeforeSave, validateWorkflowBeforeExecution } from "@/lib/validation";

// Validate before saving
const saveResult = await validateWorkflowBeforeSave(nodes, edges);

// Validate before execution (collects missing fields)
const executionResult = await validateWorkflowBeforeExecution(nodes, edges);
```

### Using the Hook

```typescript
import { useWorkflowValidation } from "@/hooks/useWorkflowValidation";

function MyComponent() {
  const { 
    validateCurrentWorkflowBeforeSave,
    validateCurrentWorkflowBeforeExecution,
    isValid,
    errors,
    missingFields
  } = useWorkflowValidation();

  const handleSave = async () => {
    const result = await validateCurrentWorkflowBeforeSave();
    if (result.isValid) {
      // Save workflow
    }
  };

  return (
    <div>
      {!isValid && <ValidationErrors errors={errors} />}
      {missingFields.length > 0 && <MissingFields missingFields={missingFields} />}
    </div>
  );
}
```

## Validation Rules

### Node Validation
- Validates node ID, type, position, and data
- Checks for duplicate node IDs
- Validates node data structure

### Edge Validation
- Validates edge ID, source, and target
- Checks for duplicate edge IDs
- Validates that source and target nodes exist
- Checks for self-referencing edges

### Connectivity Validation
- Validates that the workflow has a StartNode
- Checks that all nodes are connected to the StartNode
- Detects cycles in the workflow graph

### Field Validation
- Validates required fields
- Validates string length, number range, and pattern matching
- Validates object fields with required keys
- Collects missing required fields for execution

## Feature Flags

The validation system uses feature flags to control its behavior:

- `FRONTEND_VALIDATION`: Enable frontend validation (default: true)
- `BACKEND_VALIDATION`: Enable backend validation (default: false)
- `HYBRID_VALIDATION`: Enable hybrid validation (default: false)
- `VALIDATE_ON_EDIT`: Enable validation during editing (default: true)
- `VALIDATE_ON_SAVE`: Enable validation before saving (default: true)
- `VALIDATE_ON_EXECUTE`: Enable validation before execution (default: true)

These flags can be configured in `src/config/features.ts`.
