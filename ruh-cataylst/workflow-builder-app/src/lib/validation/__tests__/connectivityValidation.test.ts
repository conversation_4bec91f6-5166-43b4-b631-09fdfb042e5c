import { describe, it, expect } from "jest";
import { validateStartNode, validateConnectivity, detectCyclesInWorkflow } from "../connectivityValidation";
import { ValidationErrorCode } from "../types";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

describe("Connectivity Validation", () => {
  describe("validateStartNode", () => {
    it("should validate a workflow with a StartNode", () => {
      const nodes = [
        {
          id: "start-node",
          data: {
            originalType: "StartNode",
          },
        },
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
      ] as Node<WorkflowNodeData>[];

      const result = validateStartNode(nodes);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.startNodeId).toBe("start-node");
    });

    it("should detect missing StartNode", () => {
      const nodes = [
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
        {
          id: "node-2",
          data: {
            originalType: "NumberNode",
          },
        },
      ] as Node<WorkflowNodeData>[];

      const result = validateStartNode(nodes);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.WORKFLOW_MISSING_START_NODE);
    });
  });

  describe("validateConnectivity", () => {
    it("should validate a fully connected workflow", () => {
      const nodes = [
        {
          id: "start-node",
          data: {
            originalType: "StartNode",
          },
        },
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
        {
          id: "node-2",
          data: {
            originalType: "NumberNode",
          },
        },
      ] as Node<WorkflowNodeData>[];

      const edges = [
        { id: "edge-1", source: "start-node", target: "node-1" },
        { id: "edge-2", source: "node-1", target: "node-2" },
      ] as Edge[];

      const result = validateConnectivity(nodes, edges, "start-node");
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.connectedNodes?.size).toBe(3);
      expect(result.connectedNodes?.has("start-node")).toBe(true);
      expect(result.connectedNodes?.has("node-1")).toBe(true);
      expect(result.connectedNodes?.has("node-2")).toBe(true);
    });

    it("should add warnings for disconnected nodes but not fail validation", () => {
      const nodes = [
        {
          id: "start-node",
          data: {
            originalType: "StartNode",
          },
        },
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
        {
          id: "node-2",
          data: {
            originalType: "NumberNode",
          },
        },
        {
          id: "node-3", // Disconnected node
          data: {
            originalType: "BooleanNode",
            label: "Boolean Node",
          },
        },
      ] as Node<WorkflowNodeData>[];

      const edges = [
        { id: "edge-1", source: "start-node", target: "node-1" },
        { id: "edge-2", source: "node-1", target: "node-2" },
        // node-3 is not connected
      ] as Edge[];

      const result = validateConnectivity(nodes, edges, "start-node");
      // Validation should now pass with warnings instead of errors
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0].code).toBe(ValidationErrorCode.WORKFLOW_DISCONNECTED_NODES);
      expect(result.warnings[0].message).toContain("Boolean Node");
      expect(result.infos).toHaveLength(1);
      expect(result.connectedNodes?.size).toBe(3);
      expect(result.connectedNodes?.has("start-node")).toBe(true);
      expect(result.connectedNodes?.has("node-1")).toBe(true);
      expect(result.connectedNodes?.has("node-2")).toBe(true);
      expect(result.connectedNodes?.has("node-3")).toBe(false);
    });
  });

  describe("detectCyclesInWorkflow", () => {
    it("should not detect cycles in a workflow without cycles", () => {
      const nodes = [
        {
          id: "start-node",
          data: {
            originalType: "StartNode",
          },
        },
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
        {
          id: "node-2",
          data: {
            originalType: "NumberNode",
          },
        },
      ] as Node<WorkflowNodeData>[];

      const edges = [
        { id: "edge-1", source: "start-node", target: "node-1" },
        { id: "edge-2", source: "node-1", target: "node-2" },
      ] as Edge[];

      const result = detectCyclesInWorkflow(nodes, edges);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(0);
    });

    it("should detect cycles in a workflow with cycles", () => {
      const nodes = [
        {
          id: "start-node",
          data: {
            originalType: "StartNode",
          },
        },
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
        {
          id: "node-2",
          data: {
            originalType: "NumberNode",
          },
        },
      ] as Node<WorkflowNodeData>[];

      const edges = [
        { id: "edge-1", source: "start-node", target: "node-1" },
        { id: "edge-2", source: "node-1", target: "node-2" },
        { id: "edge-3", source: "node-2", target: "node-1" }, // Creates a cycle
      ] as Edge[];

      const result = detectCyclesInWorkflow(nodes, edges);
      expect(result.isValid).toBe(false);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0].code).toBe(ValidationErrorCode.WORKFLOW_CYCLE_DETECTED);
    });
  });
});
