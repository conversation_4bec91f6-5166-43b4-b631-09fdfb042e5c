import { describe, it, expect } from "jest";
import { validateEdge, validateEdges, validateEdgeUniqueness } from "../edgeValidation";
import { ValidationErrorCode } from "../types";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

describe("Edge Validation", () => {
  describe("validateEdge", () => {
    it("should validate a valid edge", () => {
      const edge = {
        id: "edge-1",
        source: "node-1",
        target: "node-2",
      } as Edge;

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
      ] as Node<WorkflowNodeData>[];

      const errors = validateEdge(edge, nodes, 0);
      expect(errors).toHaveLength(0);
    });

    it("should detect missing id", () => {
      const edge = {
        source: "node-1",
        target: "node-2",
      } as any;

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
      ] as Node<WorkflowNodeData>[];

      const errors = validateEdge(edge, nodes, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.EDGE_MISSING_ID);
    });

    it("should detect missing source", () => {
      const edge = {
        id: "edge-1",
        target: "node-2",
      } as any;

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
      ] as Node<WorkflowNodeData>[];

      const errors = validateEdge(edge, nodes, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.EDGE_MISSING_SOURCE);
    });

    it("should detect missing target", () => {
      const edge = {
        id: "edge-1",
        source: "node-1",
      } as any;

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
      ] as Node<WorkflowNodeData>[];

      const errors = validateEdge(edge, nodes, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.EDGE_MISSING_TARGET);
    });

    it("should detect non-existent source node", () => {
      const edge = {
        id: "edge-1",
        source: "non-existent",
        target: "node-2",
      } as Edge;

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
      ] as Node<WorkflowNodeData>[];

      const errors = validateEdge(edge, nodes, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.EDGE_SOURCE_NOT_FOUND);
    });

    it("should detect non-existent target node", () => {
      const edge = {
        id: "edge-1",
        source: "node-1",
        target: "non-existent",
      } as Edge;

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
      ] as Node<WorkflowNodeData>[];

      const errors = validateEdge(edge, nodes, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.EDGE_TARGET_NOT_FOUND);
    });

    it("should detect self-referencing edge", () => {
      const edge = {
        id: "edge-1",
        source: "node-1",
        target: "node-1",
      } as Edge;

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
      ] as Node<WorkflowNodeData>[];

      const errors = validateEdge(edge, nodes, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.EDGE_SELF_REFERENCE);
    });
  });

  describe("validateEdgeUniqueness", () => {
    it("should validate edges with unique IDs", () => {
      const edges = [
        { id: "edge-1", source: "node-1", target: "node-2" },
        { id: "edge-2", source: "node-2", target: "node-3" },
      ] as Edge[];

      const errors = validateEdgeUniqueness(edges);
      expect(errors).toHaveLength(0);
    });

    it("should detect duplicate edge IDs", () => {
      const edges = [
        { id: "edge-1", source: "node-1", target: "node-2" },
        { id: "edge-1", source: "node-2", target: "node-3" }, // Duplicate ID
      ] as Edge[];

      const errors = validateEdgeUniqueness(edges);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.EDGE_DUPLICATE_ID);
      expect(errors[0].message).toContain("edge-1");
    });
  });

  describe("validateEdges", () => {
    it("should validate an array of valid edges", () => {
      const edges = [
        { id: "edge-1", source: "node-1", target: "node-2" },
        { id: "edge-2", source: "node-2", target: "node-3" },
      ] as Edge[];

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
        { id: "node-3" },
      ] as Node<WorkflowNodeData>[];

      const result = validateEdges(edges, nodes);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should detect invalid edges", () => {
      const edges = [
        { id: "edge-1", source: "node-1", target: "node-2" },
        { source: "node-2", target: "node-3" }, // Missing ID
      ] as any[];

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
        { id: "node-3" },
      ] as Node<WorkflowNodeData>[];

      const result = validateEdges(edges, nodes);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.EDGE_MISSING_ID);
    });

    it("should detect duplicate edge IDs", () => {
      const edges = [
        { id: "edge-1", source: "node-1", target: "node-2" },
        { id: "edge-1", source: "node-2", target: "node-3" }, // Duplicate ID
      ] as Edge[];

      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
        { id: "node-3" },
      ] as Node<WorkflowNodeData>[];

      const result = validateEdges(edges, nodes);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.EDGE_DUPLICATE_ID);
    });
  });
});
