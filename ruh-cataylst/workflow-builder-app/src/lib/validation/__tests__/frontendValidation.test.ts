import { describe, it, expect, jest } from "jest";
import { validateWorkflowSmart, validateWorkflowBeforeSave, validateWorkflowBeforeExecution, validateWorkflowDuringEditing } from "../smartValidation";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { FEATURES } from "@/config/features";

// Mock the imported validation functions
jest.mock("../workflowValidation", () => ({
  validateWorkflow: jest.fn().mockReturnValue({
    isValid: true,
    errors: [],
    warnings: [],
    infos: [],
    missingFields: [],
  }),
}));

// Mock the feature flags
jest.mock("@/config/features", () => ({
  FEATURES: {
    FRONTEND_VALIDATION: true,
    BACKEND_VALIDATION: false,
    HYBRID_VALIDATION: false,
    VALIDATE_ON_EDIT: true,
    VALIDATE_ON_SAVE: true,
    VALIDATE_ON_EXECUTE: true,
  },
}));

describe("Frontend Validation", () => {
  const mockNodes = [
    {
      id: "node-1",
      data: {
        originalType: "StartNode",
      },
    },
  ] as Node<WorkflowNodeData>[];

  const mockEdges = [
    { id: "edge-1", source: "node-1", target: "node-2" },
  ] as Edge[];

  describe("validateWorkflowSmart", () => {
    it("should use frontend validation", async () => {
      const { validateWorkflow } = require("../workflowValidation");
      
      const result = await validateWorkflowSmart(mockNodes, mockEdges);
      
      expect(validateWorkflow).toHaveBeenCalledWith(mockNodes, mockEdges, undefined);
      expect(result.isValid).toBe(true);
    });
  });

  describe("validateWorkflowBeforeSave", () => {
    it("should validate with appropriate options", async () => {
      const { validateWorkflow } = require("../workflowValidation");
      
      const result = await validateWorkflowBeforeSave(mockNodes, mockEdges);
      
      expect(validateWorkflow).toHaveBeenCalledWith(
        mockNodes,
        mockEdges,
        expect.objectContaining({
          validateConnectivity: true,
          collectMissingFields: false,
          validateFieldTypes: true,
          validateCycles: true,
        })
      );
      expect(result.isValid).toBe(true);
    });

    it("should skip validation if VALIDATE_ON_SAVE is false", async () => {
      const { validateWorkflow } = require("../workflowValidation");
      
      // Temporarily override the feature flag
      const originalValue = FEATURES.VALIDATE_ON_SAVE;
      (FEATURES as any).VALIDATE_ON_SAVE = false;
      
      const result = await validateWorkflowBeforeSave(mockNodes, mockEdges);
      
      // Restore the feature flag
      (FEATURES as any).VALIDATE_ON_SAVE = originalValue;
      
      expect(validateWorkflow).not.toHaveBeenCalled();
      expect(result.isValid).toBe(true);
    });
  });

  describe("validateWorkflowBeforeExecution", () => {
    it("should validate with appropriate options", async () => {
      const { validateWorkflow } = require("../workflowValidation");
      
      const result = await validateWorkflowBeforeExecution(mockNodes, mockEdges);
      
      expect(validateWorkflow).toHaveBeenCalledWith(
        mockNodes,
        mockEdges,
        expect.objectContaining({
          validateConnectivity: true,
          collectMissingFields: true,
          validateFieldTypes: true,
          validateCycles: true,
        })
      );
      expect(result.isValid).toBe(true);
    });

    it("should skip validation if VALIDATE_ON_EXECUTE is false", async () => {
      const { validateWorkflow } = require("../workflowValidation");
      
      // Temporarily override the feature flag
      const originalValue = FEATURES.VALIDATE_ON_EXECUTE;
      (FEATURES as any).VALIDATE_ON_EXECUTE = false;
      
      const result = await validateWorkflowBeforeExecution(mockNodes, mockEdges);
      
      // Restore the feature flag
      (FEATURES as any).VALIDATE_ON_EXECUTE = originalValue;
      
      expect(validateWorkflow).not.toHaveBeenCalled();
      expect(result.isValid).toBe(true);
    });
  });

  describe("validateWorkflowDuringEditing", () => {
    it("should validate with appropriate options", async () => {
      const { validateWorkflow } = require("../workflowValidation");
      
      const result = await validateWorkflowDuringEditing(mockNodes, mockEdges);
      
      expect(validateWorkflow).toHaveBeenCalledWith(
        mockNodes,
        mockEdges,
        expect.objectContaining({
          validateConnectivity: false,
          collectMissingFields: false,
          validateFieldTypes: true,
          validateCycles: false,
        })
      );
      expect(result.isValid).toBe(true);
    });

    it("should skip validation if VALIDATE_ON_EDIT is false", async () => {
      const { validateWorkflow } = require("../workflowValidation");
      
      // Temporarily override the feature flag
      const originalValue = FEATURES.VALIDATE_ON_EDIT;
      (FEATURES as any).VALIDATE_ON_EDIT = false;
      
      const result = await validateWorkflowDuringEditing(mockNodes, mockEdges);
      
      // Restore the feature flag
      (FEATURES as any).VALIDATE_ON_EDIT = originalValue;
      
      expect(validateWorkflow).not.toHaveBeenCalled();
      expect(result.isValid).toBe(true);
    });
  });
});
