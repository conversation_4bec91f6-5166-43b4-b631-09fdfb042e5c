import { describe, it, expect, jest } from "jest";
import { validateWorkflowFrontend } from "../frontendValidationAdapter";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

// Mock the imported validation functions
jest.mock("../smartValidation", () => ({
  validateWorkflowBeforeExecution: jest.fn().mockResolvedValue({
    isValid: true,
    errors: [],
    warnings: [],
    infos: [],
    missingFields: [
      {
        nodeId: "node-1",
        nodeName: "Test Node",
        name: "text",
        displayName: "Text Field",
        inputType: "string",
      },
    ],
  }),
  convertToBackendFormat: jest.fn().mockReturnValue({
    is_valid: true,
    missing_fields: [
      {
        node_id: "node-1",
        node_name: "Test Node",
        name: "text",
        display_name: "Text Field",
        input_type: "string",
      },
    ],
  }),
}));

describe("Frontend Validation Adapter", () => {
  const mockNodes = [
    {
      id: "node-1",
      data: {
        originalType: "TextNode",
      },
    },
  ] as Node<WorkflowNodeData>[];

  const mockEdges = [
    { id: "edge-1", source: "start-node", target: "node-1" },
  ] as Edge[];

  describe("validateWorkflowFrontend", () => {
    it("should return validation result in backend format", async () => {
      const { validateWorkflowBeforeExecution, convertToBackendFormat } = require("../smartValidation");
      
      const result = await validateWorkflowFrontend(mockNodes, mockEdges);
      
      expect(validateWorkflowBeforeExecution).toHaveBeenCalledWith(mockNodes, mockEdges);
      expect(convertToBackendFormat).toHaveBeenCalled();
      
      expect(result).toEqual({
        is_valid: true,
        missing_fields: [
          {
            node_id: "node-1",
            node_name: "Test Node",
            name: "text",
            display_name: "Text Field",
            input_type: "string",
          },
        ],
      });
    });

    it("should handle errors gracefully", async () => {
      const { validateWorkflowBeforeExecution } = require("../smartValidation");
      
      // Make the validation function throw an error
      validateWorkflowBeforeExecution.mockRejectedValueOnce(new Error("Test error"));
      
      const result = await validateWorkflowFrontend(mockNodes, mockEdges);
      
      expect(result).toEqual({
        is_valid: false,
        error: "Test error",
      });
    });
  });
});
