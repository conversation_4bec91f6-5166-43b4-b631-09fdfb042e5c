import { describe, it, expect, beforeEach } from 'vitest';
import { isStartNode, findStartNode } from '../utils';
import { validateStartNode } from '../connectivityValidation';
import { Node } from 'reactflow';
import { WorkflowNodeData } from '@/types';
import { ValidationErrorCode } from '../types';

describe('Start Node Detection', () => {
  // Test isStartNode function
  describe('isStartNode', () => {
    it('should identify a node as a StartNode by originalType', () => {
      const node = {
        id: 'node1',
        data: {
          originalType: 'StartNode',
        }
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(true);
    });

    it('should identify a node as a StartNode by definition name', () => {
      const node = {
        id: 'node1',
        data: {
          definition: {
            name: 'StartNode'
          }
        }
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(true);
    });

    it('should identify a node as a StartNode by node type', () => {
      const node = {
        id: 'node1',
        type: 'StartNode',
        data: {}
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(true);
    });

    it('should identify a node as a StartNode by data type', () => {
      const node = {
        id: 'node1',
        data: {
          type: 'StartNode'
        }
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(true);
    });

    it('should identify a node as a StartNode by label', () => {
      const node = {
        id: 'node1',
        data: {
          label: 'Start'
        }
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(true);
    });

    it('should identify a node as a StartNode by component name pattern', () => {
      const node = {
        id: 'node1',
        data: {
          type: 'component',
          definition: {
            name: 'CustomStartComponent'
          }
        }
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(true);
    });

    it('should handle null or undefined node data', () => {
      const nullDataNode = {
        id: 'node1',
        data: null
      } as unknown as Node<WorkflowNodeData>;
      
      const undefinedDataNode = {
        id: 'node2'
      } as unknown as Node<WorkflowNodeData>;
      
      expect(isStartNode(nullDataNode)).toBe(false);
      expect(isStartNode(undefinedDataNode)).toBe(false);
      expect(isStartNode(null as unknown as Node<WorkflowNodeData>)).toBe(false);
      expect(isStartNode(undefined as unknown as Node<WorkflowNodeData>)).toBe(false);
    });

    it('should return false for non-StartNode nodes', () => {
      const node = {
        id: 'node1',
        data: {
          originalType: 'TextNode',
          type: 'TextNode',
          definition: {
            name: 'TextNode'
          },
          label: 'Text Node'
        }
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(false);
    });
  });

  // Test findStartNode function
  describe('findStartNode', () => {
    it('should find a StartNode in an array of nodes', () => {
      const nodes = [
        {
          id: 'node1',
          data: {
            originalType: 'TextNode'
          }
        },
        {
          id: 'node2',
          data: {
            originalType: 'StartNode'
          }
        },
        {
          id: 'node3',
          data: {
            originalType: 'ImageNode'
          }
        }
      ] as Node<WorkflowNodeData>[];
      
      const startNode = findStartNode(nodes);
      expect(startNode).toBeDefined();
      expect(startNode?.id).toBe('node2');
    });

    it('should find a StartNode using any of the detection methods', () => {
      const nodes = [
        {
          id: 'node1',
          data: {
            originalType: 'TextNode'
          }
        },
        {
          id: 'node2',
          data: {
            label: 'Start'
          }
        },
        {
          id: 'node3',
          data: {
            originalType: 'ImageNode'
          }
        }
      ] as Node<WorkflowNodeData>[];
      
      const startNode = findStartNode(nodes);
      expect(startNode).toBeDefined();
      expect(startNode?.id).toBe('node2');
    });

    it('should return undefined if no StartNode is found', () => {
      const nodes = [
        {
          id: 'node1',
          data: {
            originalType: 'TextNode'
          }
        },
        {
          id: 'node2',
          data: {
            originalType: 'ImageNode'
          }
        }
      ] as Node<WorkflowNodeData>[];
      
      const startNode = findStartNode(nodes);
      expect(startNode).toBeUndefined();
    });

    it('should handle empty nodes array', () => {
      const nodes: Node<WorkflowNodeData>[] = [];
      const startNode = findStartNode(nodes);
      expect(startNode).toBeUndefined();
    });

    it('should handle null or undefined nodes array', () => {
      expect(findStartNode(null as unknown as Node<WorkflowNodeData>[])).toBeUndefined();
      expect(findStartNode(undefined as unknown as Node<WorkflowNodeData>[])).toBeUndefined();
    });
  });

  // Test validateStartNode function
  describe('validateStartNode', () => {
    it('should validate a workflow with a StartNode', () => {
      const nodes = [
        {
          id: 'start-node',
          data: {
            originalType: 'StartNode'
          }
        },
        {
          id: 'node-1',
          data: {
            originalType: 'TextNode'
          }
        }
      ] as Node<WorkflowNodeData>[];

      const result = validateStartNode(nodes);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.startNodeId).toBe('start-node');
    });

    it('should fail validation for a workflow without a StartNode', () => {
      const nodes = [
        {
          id: 'node-1',
          data: {
            originalType: 'TextNode'
          }
        },
        {
          id: 'node-2',
          data: {
            originalType: 'ImageNode'
          }
        }
      ] as Node<WorkflowNodeData>[];

      const result = validateStartNode(nodes);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.WORKFLOW_MISSING_START_NODE);
    });

    it('should handle empty nodes array', () => {
      const nodes: Node<WorkflowNodeData>[] = [];
      const result = validateStartNode(nodes);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.WORKFLOW_EMPTY);
    });

    it('should find a StartNode using fallback detection', () => {
      const nodes = [
        {
          id: 'node-1',
          data: {
            originalType: 'TextNode'
          }
        },
        {
          id: 'start-like-node',
          data: {
            label: 'Start',
            originalType: 'CustomNode'
          }
        }
      ] as Node<WorkflowNodeData>[];

      const result = validateStartNode(nodes);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0].code).toBe(ValidationErrorCode.WORKFLOW_USING_FALLBACK_START_NODE);
      expect(result.startNodeId).toBe('start-like-node');
    });
  });
});
