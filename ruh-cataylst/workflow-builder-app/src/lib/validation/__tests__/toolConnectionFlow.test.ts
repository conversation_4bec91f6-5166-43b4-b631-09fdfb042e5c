/**
 * Tests for tool connection flow integration
 */

import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { 
  getConnectedNodesWithToolConnections,
  isNodeConnectedViaTools,
  getToolConnectedComponents,
  validateToolConnectionFlow
} from "../toolConnectionFlow";

// Mock tool connection utilities
jest.mock("@/utils/toolConnectionUtils", () => ({
  isToolHandle: jest.fn(),
  getNodesConnectedAsTools: jest.fn(),
}));

import { isToolHandle, getNodesConnectedAsTools } from "@/utils/toolConnectionUtils";

const mockIsToolHandle = isToolHandle as jest.MockedFunction<typeof isToolHandle>;
const mockGetNodesConnectedAsTools = getNodesConnectedAsTools as jest.MockedFunction<typeof getNodesConnectedAsTools>;

// Helper function to create test nodes
const createTestNode = (id: string, originalType: string): Node<WorkflowNodeData> => ({
  id,
  type: "WorkflowNode",
  position: { x: 0, y: 0 },
  data: {
    label: `${originalType} Node`,
    type: "component",
    originalType,
    definition: { name: originalType },
    config: {},
  },
});

// Helper function to create test edges
const createTestEdge = (source: string, target: string, targetHandle?: string): Edge => ({
  id: `${source}-${target}`,
  source,
  target,
  targetHandle,
});

describe("Tool Connection Flow Integration", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockIsToolHandle.mockImplementation((handleName) => handleName?.startsWith("tool_"));
    mockGetNodesConnectedAsTools.mockReturnValue(new Set());
  });

  describe("getConnectedNodesWithToolConnections", () => {
    test("includes nodes connected via regular edges", () => {
      const nodes = [
        createTestNode("start", "StartNode"),
        createTestNode("node1", "DataProcessor"),
        createTestNode("node2", "TextAnalyzer"),
      ];

      const edges = [
        createTestEdge("start", "node1"),
        createTestEdge("node1", "node2"),
      ];

      const connectedNodes = getConnectedNodesWithToolConnections(nodes, edges, "start");

      expect(connectedNodes).toContain("start");
      expect(connectedNodes).toContain("node1");
      expect(connectedNodes).toContain("node2");
      expect(connectedNodes.size).toBe(3);
    });

    test("includes nodes connected via tool handles", () => {
      const nodes = [
        createTestNode("start", "StartNode"),
        createTestNode("agent", "AgenticAI"),
        createTestNode("tool1", "DataProcessor"),
        createTestNode("tool2", "TextAnalyzer"),
      ];

      const edges = [
        createTestEdge("start", "agent"),
        createTestEdge("tool1", "agent", "tool_1"),
        createTestEdge("tool2", "agent", "tool_2"),
      ];

      const connectedNodes = getConnectedNodesWithToolConnections(nodes, edges, "start");

      expect(connectedNodes).toContain("start");
      expect(connectedNodes).toContain("agent");
      expect(connectedNodes).toContain("tool1");
      expect(connectedNodes).toContain("tool2");
      expect(connectedNodes.size).toBe(4);
    });

    test("includes nodes in complex tool connection chains", () => {
      const nodes = [
        createTestNode("start", "StartNode"),
        createTestNode("agent1", "AgenticAI"),
        createTestNode("agent2", "AgenticAI"),
        createTestNode("tool1", "DataProcessor"),
        createTestNode("tool2", "TextAnalyzer"),
        createTestNode("disconnected", "Isolated"),
      ];

      const edges = [
        createTestEdge("start", "agent1"),
        createTestEdge("agent1", "agent2"),
        createTestEdge("tool1", "agent1", "tool_1"),
        createTestEdge("tool2", "agent2", "tool_1"),
      ];

      const connectedNodes = getConnectedNodesWithToolConnections(nodes, edges, "start");

      expect(connectedNodes).toContain("start");
      expect(connectedNodes).toContain("agent1");
      expect(connectedNodes).toContain("agent2");
      expect(connectedNodes).toContain("tool1");
      expect(connectedNodes).toContain("tool2");
      expect(connectedNodes).not.toContain("disconnected");
      expect(connectedNodes.size).toBe(5);
    });

    test("handles circular tool connections correctly", () => {
      const nodes = [
        createTestNode("start", "StartNode"),
        createTestNode("agent1", "AgenticAI"),
        createTestNode("agent2", "AgenticAI"),
      ];

      const edges = [
        createTestEdge("start", "agent1"),
        createTestEdge("agent1", "agent2"),
        createTestEdge("agent2", "agent1", "tool_1"),
      ];

      const connectedNodes = getConnectedNodesWithToolConnections(nodes, edges, "start");

      expect(connectedNodes).toContain("start");
      expect(connectedNodes).toContain("agent1");
      expect(connectedNodes).toContain("agent2");
      expect(connectedNodes.size).toBe(3);
    });
  });

  describe("isNodeConnectedViaTools", () => {
    test("returns true for nodes connected as tools", () => {
      const nodes = [
        createTestNode("agent", "AgenticAI"),
        createTestNode("tool", "DataProcessor"),
      ];

      const edges = [
        createTestEdge("tool", "agent", "tool_1"),
      ];

      const result = isNodeConnectedViaTools("tool", nodes, edges);

      expect(result).toBe(true);
    });

    test("returns false for nodes not connected as tools", () => {
      const nodes = [
        createTestNode("agent", "AgenticAI"),
        createTestNode("regular", "DataProcessor"),
      ];

      const edges = [
        createTestEdge("regular", "agent", "input_data"),
      ];

      const result = isNodeConnectedViaTools("regular", nodes, edges);

      expect(result).toBe(false);
    });

    test("returns false for disconnected nodes", () => {
      const nodes = [
        createTestNode("agent", "AgenticAI"),
        createTestNode("isolated", "DataProcessor"),
      ];

      const edges: Edge[] = [];

      const result = isNodeConnectedViaTools("isolated", nodes, edges);

      expect(result).toBe(false);
    });
  });

  describe("getToolConnectedComponents", () => {
    test("returns all components connected as tools", () => {
      const nodes = [
        createTestNode("agent1", "AgenticAI"),
        createTestNode("agent2", "AgenticAI"),
        createTestNode("tool1", "DataProcessor"),
        createTestNode("tool2", "TextAnalyzer"),
        createTestNode("regular", "FileReader"),
      ];

      const edges = [
        createTestEdge("tool1", "agent1", "tool_1"),
        createTestEdge("tool2", "agent2", "tool_1"),
        createTestEdge("regular", "agent1", "input_data"),
      ];

      const toolConnectedComponents = getToolConnectedComponents(nodes, edges);

      expect(toolConnectedComponents).toContain("tool1");
      expect(toolConnectedComponents).toContain("tool2");
      expect(toolConnectedComponents).not.toContain("regular");
      expect(toolConnectedComponents).not.toContain("agent1");
      expect(toolConnectedComponents).not.toContain("agent2");
      expect(toolConnectedComponents.size).toBe(2);
    });

    test("returns empty set when no tool connections exist", () => {
      const nodes = [
        createTestNode("agent", "AgenticAI"),
        createTestNode("regular", "DataProcessor"),
      ];

      const edges = [
        createTestEdge("regular", "agent", "input_data"),
      ];

      const toolConnectedComponents = getToolConnectedComponents(nodes, edges);

      expect(toolConnectedComponents.size).toBe(0);
    });
  });

  describe("validateToolConnectionFlow", () => {
    test("validates workflow with tool connections successfully", () => {
      const nodes = [
        createTestNode("start", "StartNode"),
        createTestNode("agent", "AgenticAI"),
        createTestNode("tool", "DataProcessor"),
      ];

      const edges = [
        createTestEdge("start", "agent"),
        createTestEdge("tool", "agent", "tool_1"),
      ];

      const result = validateToolConnectionFlow(nodes, edges);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.connectedNodes).toContain("start");
      expect(result.connectedNodes).toContain("agent");
      expect(result.connectedNodes).toContain("tool");
    });

    test("reports warnings for disconnected tool components", () => {
      const nodes = [
        createTestNode("start", "StartNode"),
        createTestNode("agent", "AgenticAI"),
        createTestNode("tool", "DataProcessor"),
        createTestNode("isolated", "TextAnalyzer"),
      ];

      const edges = [
        createTestEdge("start", "agent"),
        createTestEdge("tool", "agent", "tool_1"),
      ];

      const result = validateToolConnectionFlow(nodes, edges);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0].message).toContain("TextAnalyzer Node");
      expect(result.connectedNodes).not.toContain("isolated");
    });

    test("handles complex workflows with multiple tool connections", () => {
      const nodes = [
        createTestNode("start", "StartNode"),
        createTestNode("agent1", "AgenticAI"),
        createTestNode("agent2", "AgenticAI"),
        createTestNode("tool1", "DataProcessor"),
        createTestNode("tool2", "TextAnalyzer"),
        createTestNode("tool3", "FileWriter"),
      ];

      const edges = [
        createTestEdge("start", "agent1"),
        createTestEdge("agent1", "agent2"),
        createTestEdge("tool1", "agent1", "tool_1"),
        createTestEdge("tool2", "agent1", "tool_2"),
        createTestEdge("tool3", "agent2", "tool_1"),
      ];

      const result = validateToolConnectionFlow(nodes, edges);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.connectedNodes?.size).toBe(6);
    });
  });

  describe("Performance", () => {
    test("handles large workflows efficiently", () => {
      const nodes = Array.from({ length: 100 }, (_, i) => 
        createTestNode(`node${i}`, i === 0 ? "StartNode" : "DataProcessor")
      );

      const edges = Array.from({ length: 99 }, (_, i) => 
        createTestEdge(`node${i}`, `node${i + 1}`)
      );

      const startTime = performance.now();
      const connectedNodes = getConnectedNodesWithToolConnections(nodes, edges, "node0");
      const endTime = performance.now();

      expect(connectedNodes.size).toBe(100);
      expect(endTime - startTime).toBeLessThan(100); // Should complete in <100ms
    });
  });
});
