import { describe, it, expect } from "jest";
import { ValidationErrorCode, ValidationSeverity } from "../types";

// This test file is primarily to ensure that our type definitions are correct
// and that we don't accidentally change the values of our enums.
describe("Validation Types", () => {
  describe("ValidationErrorCode", () => {
    it("should define all required workflow validation error codes", () => {
      expect(ValidationErrorCode.WORKFLOW_INVALID_JSON).toBe("WF001");
      expect(ValidationErrorCode.WORKFLOW_MISSING_NODES).toBe("WF002");
      expect(ValidationErrorCode.WORKFLOW_MISSING_EDGES).toBe("WF003");
      expect(ValidationErrorCode.WORKFLOW_MISSING_START_NODE).toBe("WF004");
      expect(ValidationErrorCode.WORKFLOW_DISCONNECTED_NODES).toBe("WF005");
      expect(ValidationErrorCode.WORKFLOW_CYCLE_DETECTED).toBe("WF006");
      expect(ValidationErrorCode.WORKFLOW_INVALID_NAME).toBe("WF007");
    });

    it("should define all required node validation error codes", () => {
      expect(ValidationErrorCode.NODE_MISSING_ID).toBe("ND001");
      expect(ValidationErrorCode.NODE_MISSING_TYPE).toBe("ND002");
      expect(ValidationErrorCode.NODE_MISSING_POSITION).toBe("ND003");
      expect(ValidationErrorCode.NODE_MISSING_DATA).toBe("ND004");
      expect(ValidationErrorCode.NODE_MISSING_DATA_TYPE).toBe("ND005");
      expect(ValidationErrorCode.NODE_MISSING_DATA_LABEL).toBe("ND006");
      expect(ValidationErrorCode.NODE_MISSING_DATA_DEFINITION).toBe("ND007");
      expect(ValidationErrorCode.NODE_DUPLICATE_ID).toBe("ND008");
      expect(ValidationErrorCode.NODE_INVALID_POSITION).toBe("ND009");
    });

    it("should define all required edge validation error codes", () => {
      expect(ValidationErrorCode.EDGE_MISSING_ID).toBe("ED001");
      expect(ValidationErrorCode.EDGE_MISSING_SOURCE).toBe("ED002");
      expect(ValidationErrorCode.EDGE_MISSING_TARGET).toBe("ED003");
      expect(ValidationErrorCode.EDGE_SOURCE_NOT_FOUND).toBe("ED004");
      expect(ValidationErrorCode.EDGE_TARGET_NOT_FOUND).toBe("ED005");
      expect(ValidationErrorCode.EDGE_DUPLICATE_ID).toBe("ED006");
      expect(ValidationErrorCode.EDGE_SELF_REFERENCE).toBe("ED007");
    });

    it("should define all required field validation error codes", () => {
      expect(ValidationErrorCode.FIELD_REQUIRED).toBe("FD001");
      expect(ValidationErrorCode.FIELD_STRING_LENGTH).toBe("FD002");
      expect(ValidationErrorCode.FIELD_NUMBER_RANGE).toBe("FD003");
      expect(ValidationErrorCode.FIELD_PATTERN_MISMATCH).toBe("FD004");
      expect(ValidationErrorCode.FIELD_MISSING_REQUIRED_KEYS).toBe("FD005");
      expect(ValidationErrorCode.FIELD_CONNECTED_INPUT).toBe("FD006");
    });
  });

  describe("ValidationSeverity", () => {
    it("should define all required severity levels", () => {
      const severities: ValidationSeverity[] = ["error", "warning", "info"];
      expect(severities).toContain("error");
      expect(severities).toContain("warning");
      expect(severities).toContain("info");
    });
  });
});
