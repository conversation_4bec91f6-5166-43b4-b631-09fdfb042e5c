import { describe, it, expect, beforeEach } from 'vitest';
import { validateWorkflow } from '../workflowValidation';
import { validateWorkflowBeforeExecution } from '../smartValidation';
import { Node, Edge } from 'reactflow';
import { WorkflowNodeData } from '@/types';

// Mock the feature flags
jest.mock('@/config/features', () => ({
  FEATURES: {
    FRONTEND_VALIDATION: true,
    BACKEND_VALIDATION: false,
    HYBRID_VALIDATION: false,
    VALIDATION_DEBUG: false,
    VALIDATE_ON_EDIT: true,
    VALIDATE_ON_SAVE: true,
    VALIDATE_ON_EXECUTE: true,
  }
}));

describe('Workflow Validation Integration Tests', () => {
  // Helper function to create a basic workflow with a Start node
  const createBasicWorkflow = () => {
    const nodes: Node<WorkflowNodeData>[] = [
      {
        id: 'start-node',
        type: 'default',
        position: { x: 0, y: 0 },
        data: {
          label: 'Start Node',
          originalType: 'StartNode',
          type: 'StartNode',
          definition: {
            name: 'StartNode',
            inputs: []
          },
          config: {}
        }
      }
    ];

    const edges: Edge[] = [];

    return { nodes, edges };
  };

  // Helper function to create an MCP component node
  const createMCPNode = (id: string, label: string, inputs: any[] = [], config: any = {}) => {
    return {
      id,
      type: 'default',
      position: { x: 100, y: 0 },
      data: {
        label,
        type: 'mcp',
        originalType: 'MCPMarketplaceComponent',
        definition: {
          name: 'ScriptGenerator',
          display_name: 'Script Generator',
          inputs,
          mcp_info: {
            tool_name: 'ScriptGenerator'
          }
        },
        config
      }
    } as Node<WorkflowNodeData>;
  };

  // Helper function to create a standard component node
  const createStandardNode = (id: string, label: string, inputs: any[] = [], config: any = {}) => {
    return {
      id,
      type: 'default',
      position: { x: 100, y: 100 },
      data: {
        label,
        type: 'standard',
        originalType: 'StandardComponent',
        definition: {
          name: 'StandardComponent',
          display_name: 'Standard Component',
          inputs
        },
        config
      }
    } as Node<WorkflowNodeData>;
  };

  // Helper function to create an edge between nodes
  const createEdge = (source: string, target: string) => {
    return {
      id: `${source}-${target}`,
      source,
      target,
      type: 'default'
    } as Edge;
  };

  it('should validate a workflow with a Start node', async () => {
    const { nodes, edges } = createBasicWorkflow();

    const result = validateWorkflow(nodes, edges);

    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.startNodeId).toBe('start-node');
  });

  it('should detect a missing Start node', async () => {
    const nodes: Node<WorkflowNodeData>[] = [
      createStandardNode('node1', 'Standard Node')
    ];
    const edges: Edge[] = [];

    const result = validateWorkflow(nodes, edges);

    expect(result.isValid).toBe(false);
    expect(result.errors).toHaveLength(1);
    expect(result.errors[0].message).toContain('Start node');
  });

  it('should add warnings for disconnected nodes but not fail validation', async () => {
    const { nodes, edges } = createBasicWorkflow();

    // Add a disconnected node
    nodes.push(createStandardNode('node1', 'Disconnected Node'));

    const result = validateWorkflow(nodes, edges, { validateConnectivity: true });

    // Validation should now pass with warnings instead of errors
    expect(result.isValid).toBe(true);
    expect(result.errors.length).toBe(0);
    expect(result.warnings.some(w => w.message.includes('not connected'))).toBe(true);
    expect(result.infos.length).toBeGreaterThan(0);

    // Check that connectedNodes doesn't include the disconnected node
    expect(result.connectedNodes).toBeDefined();
    expect(result.connectedNodes!.has('node1')).toBe(false);
  });

  it('should collect missing required fields from connected nodes', async () => {
    const { nodes, edges } = createBasicWorkflow();

    // Add a connected MCP node with missing required fields
    const mcpNode = createMCPNode('mcp1', 'Script Generator', [
      {
        name: 'topic',
        display_name: 'Topic',
        input_type: 'string'
      },
      {
        name: 'script_type',
        display_name: 'Script Type',
        input_type: 'string'
      }
    ]);

    nodes.push(mcpNode);
    edges.push(createEdge('start-node', 'mcp1'));

    const result = validateWorkflow(nodes, edges, {
      validateConnectivity: true,
      collectMissingFields: true
    });

    expect(result.isValid).toBe(true); // Validation passes despite missing fields
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(2);
    expect(result.missingFields!.some(f => f.name === 'topic')).toBe(true);
    expect(result.missingFields!.some(f => f.name === 'script_type')).toBe(true);
  });

  it('should not collect missing fields from disconnected nodes', async () => {
    const { nodes, edges } = createBasicWorkflow();

    // Add a disconnected MCP node with missing required fields
    const mcpNode = createMCPNode('mcp1', 'Script Generator', [
      {
        name: 'topic',
        display_name: 'Topic',
        input_type: 'string'
      }
    ]);

    nodes.push(mcpNode);
    // No edge connecting start-node to mcp1

    const result = validateWorkflow(nodes, edges, {
      validateConnectivity: true,
      collectMissingFields: true
    });

    // Validation should now pass with warnings instead of errors
    expect(result.isValid).toBe(true);
    expect(result.warnings.some(w => w.message.includes('not connected'))).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(0); // No missing fields from disconnected node

    // Check that connectedNodes doesn't include the disconnected node
    expect(result.connectedNodes).toBeDefined();
    expect(result.connectedNodes!.has('mcp1')).toBe(false);
  });

  it('should handle MCP components with special field requirements', async () => {
    const { nodes, edges } = createBasicWorkflow();

    // Add a connected MCP node with some fields filled and some missing
    const mcpNode = createMCPNode('mcp1', 'Script Generator', [
      {
        name: 'topic',
        display_name: 'Topic',
        input_type: 'string'
      },
      {
        name: 'script_type',
        display_name: 'Script Type',
        input_type: 'string'
      },
      {
        name: 'api_key', // Common optional field
        display_name: 'API Key',
        input_type: 'string'
      },
      {
        name: 'optional_field',
        display_name: 'Optional Field',
        input_type: 'string',
        required: false
      }
    ], {
      topic: 'Test Topic' // Only topic is provided
    });

    nodes.push(mcpNode);
    edges.push(createEdge('start-node', 'mcp1'));

    const result = validateWorkflow(nodes, edges, {
      validateConnectivity: true,
      collectMissingFields: true
    });

    expect(result.isValid).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(1);
    expect(result.missingFields![0].name).toBe('script_type');
    // api_key and optional_field should not be in missing fields
  });

  it('should validate a complete workflow before execution', async () => {
    const { nodes, edges } = createBasicWorkflow();

    // Add a connected node with all required fields filled
    const standardNode = createStandardNode('node1', 'Standard Node', [
      {
        name: 'required_field',
        display_name: 'Required Field',
        input_type: 'string',
        required: true
      }
    ], {
      required_field: 'Value' // Field is provided
    });

    nodes.push(standardNode);
    edges.push(createEdge('start-node', 'node1'));

    const result = await validateWorkflowBeforeExecution(nodes, edges);

    expect(result.isValid).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(0);
  });

  it('should handle the edge case where a Start node exists but is not connected to other nodes', async () => {
    const { nodes, edges } = createBasicWorkflow();

    // Add a node but don't connect it to the Start node
    nodes.push(createStandardNode('node1', 'Standard Node', [
      {
        name: 'required_field',
        display_name: 'Required Field',
        input_type: 'string',
        required: true
      }
    ]));

    const result = validateWorkflow(nodes, edges, {
      validateConnectivity: true,
      collectMissingFields: true
    });

    // Validation should now pass with warnings instead of errors
    expect(result.isValid).toBe(true);
    expect(result.warnings.some(w => w.message.includes('not connected'))).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(0); // No missing fields since node is not connected

    // Check that connectedNodes doesn't include the disconnected node
    expect(result.connectedNodes).toBeDefined();
    expect(result.connectedNodes!.has('node1')).toBe(false);
  });
});
