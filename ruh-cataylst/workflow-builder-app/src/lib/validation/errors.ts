import { ValidationError, ValidationErrorCode, ValidationSeverity } from "./types";

/**
 * Creates a validation error object
 * 
 * @param code The error code
 * @param message The error message
 * @param severity The error severity (default: "error")
 * @param nodeId Optional node ID for node-specific errors
 * @param fieldId Optional field ID for field-specific errors
 * @returns A ValidationError object
 */
export function createValidationError(
  code: ValidationErrorCode,
  message: string,
  severity: ValidationSeverity = "error",
  nodeId?: string,
  fieldId?: string
): ValidationError {
  return {
    code,
    message,
    severity,
    nodeId,
    fieldId,
  };
}
