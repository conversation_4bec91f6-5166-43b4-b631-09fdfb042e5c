import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { ValidationError, ValidationErrorCode, ValidationResult } from "./types";
import { createValidationError } from "./errors";

/**
 * Validates a single node
 * 
 * @param node The node to validate
 * @param index The index of the node in the array
 * @returns An array of validation errors
 */
export function validateNode(
  node: Node<WorkflowNodeData>,
  index: number
): ValidationError[] {
  const errors: ValidationError[] = [];

  // Check for required properties
  if (!node.id) {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_MISSING_ID,
        `Node at index ${index} is missing an ID`
      )
    );
  }

  if (!node.type) {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_MISSING_TYPE,
        `Node ${node.id || `at index ${index}`} is missing a type`,
        "error",
        node.id
      )
    );
  }

  if (!node.position) {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_MISSING_POSITION,
        `Node ${node.id || `at index ${index}`} is missing a position`,
        "error",
        node.id
      )
    );
  } else if (
    typeof node.position !== "object" ||
    typeof node.position.x !== "number" ||
    typeof node.position.y !== "number"
  ) {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_INVALID_POSITION,
        `Node ${node.id || `at index ${index}`} has an invalid position`,
        "error",
        node.id
      )
    );
  }

  if (!node.data) {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_MISSING_DATA,
        `Node ${node.id || `at index ${index}`} is missing data`,
        "error",
        node.id
      )
    );
    return errors; // Stop validation if data is missing
  }

  // Validate data properties
  if (!node.data.type) {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_MISSING_DATA_TYPE,
        `Node ${node.id || `at index ${index}`} is missing data.type`,
        "error",
        node.id
      )
    );
  }

  if (!node.data.label) {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_MISSING_DATA_LABEL,
        `Node ${node.id || `at index ${index}`} is missing data.label`,
        "error",
        node.id
      )
    );
  }

  if (!node.data.definition) {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_MISSING_DATA_DEFINITION,
        `Node ${node.id || `at index ${index}`} is missing data.definition`,
        "error",
        node.id
      )
    );
  }

  return errors;
}

/**
 * Validates that all nodes have unique IDs
 * 
 * @param nodes The array of nodes to validate
 * @returns An array of validation errors
 */
export function validateNodeUniqueness(
  nodes: Node<WorkflowNodeData>[]
): ValidationError[] {
  const errors: ValidationError[] = [];
  const nodeIds = new Set<string>();
  const duplicateIds = new Set<string>();

  // Find duplicate IDs
  nodes.forEach(node => {
    if (node.id) {
      if (nodeIds.has(node.id)) {
        duplicateIds.add(node.id);
      } else {
        nodeIds.add(node.id);
      }
    }
  });

  // Create errors for duplicate IDs
  duplicateIds.forEach(id => {
    errors.push(
      createValidationError(
        ValidationErrorCode.NODE_DUPLICATE_ID,
        `Duplicate node ID: ${id}`,
        "error",
        id
      )
    );
  });

  return errors;
}

/**
 * Validates all nodes in a workflow
 * 
 * @param nodes The array of nodes to validate
 * @returns A validation result
 */
export function validateNodes(
  nodes: Node<WorkflowNodeData>[]
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const infos: ValidationError[] = [];

  // Check if nodes is an array
  if (!Array.isArray(nodes)) {
    errors.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_MISSING_NODES,
        'Workflow must contain a "nodes" array'
      )
    );
    return { isValid: false, errors, warnings, infos };
  }

  // Validate each node
  nodes.forEach((node, index) => {
    const nodeErrors = validateNode(node, index);
    errors.push(...nodeErrors);
  });

  // Validate node uniqueness
  const uniquenessErrors = validateNodeUniqueness(nodes);
  errors.push(...uniquenessErrors);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    infos,
  };
}
