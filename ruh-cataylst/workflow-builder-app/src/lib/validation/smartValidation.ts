import { <PERSON><PERSON>, <PERSON> } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { validateWorkflow } from "./workflowValidation";
import { ValidationResult, WorkflowValidationOptions, MissingField } from "./types";
import { FEATURES } from "@/config/features";

/**
 * Converts frontend validation result to backend-compatible format
 * This is used for backward compatibility with code that expects the backend format
 */
export function convertToBackendFormat(result: ValidationResult): {
  is_valid: boolean;
  missing_fields?: {
    node_id: string;
    node_name: string;
    name: string;
    display_name: string;
    info?: string;
    input_type: string;
  }[];
  error?: string;
} {
  // Convert missing fields to backend format
  const missingFields = result.missingFields?.map((field) => ({
    node_id: field.nodeId,
    node_name: field.nodeName,
    name: field.name,
    display_name: field.displayName,
    info: field.info,
    input_type: field.inputType,
  }));

  // If there are errors, combine them into a single error message
  let error: string | undefined;
  if (result.errors.length > 0) {
    error = result.errors.map((err) => err.message).join("; ");
  }

  return {
    is_valid: result.isValid,
    missing_fields: missingFields,
    error,
  };
}

/**
 * Smart validation function that uses the appropriate validation method
 * based on feature flags
 * 
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @param options Validation options
 * @returns A validation result
 */
export async function validateWorkflowSmart(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  options?: WorkflowValidationOptions
): Promise<ValidationResult> {
  // Always use frontend validation since backend validation is disabled
  return validateWorkflow(nodes, edges, options);
}

/**
 * Validates a workflow before saving
 * 
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns A validation result
 */
export async function validateWorkflowBeforeSave(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Promise<ValidationResult> {
  if (!FEATURES.VALIDATE_ON_SAVE) {
    // If validation on save is disabled, return a valid result
    return {
      isValid: true,
      errors: [],
      warnings: [],
      infos: [],
    };
  }

  const options: WorkflowValidationOptions = {
    validateConnectivity: true,
    collectMissingFields: false, // Don't collect missing fields for save
    validateFieldTypes: true,
    validateCycles: true,
  };

  return validateWorkflowSmart(nodes, edges, options);
}

/**
 * Validates a workflow before execution
 * 
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns A validation result with missing fields
 */
export async function validateWorkflowBeforeExecution(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Promise<ValidationResult> {
  if (!FEATURES.VALIDATE_ON_EXECUTE) {
    // If validation on execute is disabled, return a valid result
    return {
      isValid: true,
      errors: [],
      warnings: [],
      infos: [],
    };
  }

  const options: WorkflowValidationOptions = {
    validateConnectivity: true,
    collectMissingFields: true, // Collect missing fields for execution
    validateFieldTypes: true,
    validateCycles: true,
  };

  return validateWorkflowSmart(nodes, edges, options);
}

/**
 * Validates a workflow during editing
 * 
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns A validation result
 */
export async function validateWorkflowDuringEditing(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Promise<ValidationResult> {
  if (!FEATURES.VALIDATE_ON_EDIT) {
    // If validation during editing is disabled, return a valid result
    return {
      isValid: true,
      errors: [],
      warnings: [],
      infos: [],
    };
  }

  const options: WorkflowValidationOptions = {
    validateConnectivity: false, // Skip connectivity validation during editing
    collectMissingFields: false, // Don't collect missing fields during editing
    validateFieldTypes: true,
    validateCycles: false, // Skip cycle detection during editing
  };

  return validateWorkflowSmart(nodes, edges, options);
}
