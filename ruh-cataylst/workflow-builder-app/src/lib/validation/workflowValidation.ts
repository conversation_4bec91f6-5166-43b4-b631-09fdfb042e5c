import { <PERSON><PERSON>, <PERSON> } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { ValidationError, ValidationErrorCode, ValidationResult, WorkflowValidationOptions, MissingField } from "./types";
import { createValidationError } from "./errors";
import { validateNodes } from "./nodeValidation";
import { validateEdges } from "./edgeValidation";
import { validateStartNode, validateConnectivity, detectCyclesInWorkflow } from "./connectivityValidation";
import { collectMissingRequiredFields } from "./fieldValidation";

/**
 * Validates the basic structure of a workflow
 *
 * @param workflowData The workflow data to validate
 * @returns A validation result
 */
export function validateWorkflowStructure(workflowData: any): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const infos: ValidationError[] = [];

  // Check if workflow is a valid object
  if (!workflowData || typeof workflowData !== "object") {
    errors.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_INVALID_JSON,
        "Workflow must be a valid JSON object"
      )
    );
    return { isValid: false, errors, warnings, infos };
  }

  // Check if nodes array exists
  if (!workflowData.nodes || !Array.isArray(workflowData.nodes)) {
    errors.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_MISSING_NODES,
        'Workflow must contain a "nodes" array'
      )
    );
  }

  // Check if edges array exists
  if (!workflowData.edges || !Array.isArray(workflowData.edges)) {
    errors.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_MISSING_EDGES,
        'Workflow must contain an "edges" array'
      )
    );
  }

  // Check workflow_name if present
  if (workflowData.workflow_name && typeof workflowData.workflow_name !== "string") {
    warnings.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_INVALID_NAME,
        "Workflow name must be a string",
        "warning"
      )
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    infos,
  };
}

/**
 * Validates a complete workflow with enhanced initialization checks
 *
 * @param nodes The array of nodes to validate
 * @param edges The array of edges to validate
 * @param options Validation options
 * @returns A validation result
 */
export function validateWorkflow(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  options: WorkflowValidationOptions = {
    validateConnectivity: true,
    collectMissingFields: true,
    validateFieldTypes: true,
    validateCycles: true,
  }
): ValidationResult {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  console.log(`[${timestamp}] [validateWorkflow] Starting workflow validation with ${nodes?.length || 0} nodes and ${edges?.length || 0} edges`);

  // Check if nodes array is valid
  if (!nodes || !Array.isArray(nodes)) {
    console.error(`[${timestamp}] [validateWorkflow] Nodes array is invalid or undefined`);
    return {
      isValid: false,
      errors: [
        createValidationError(
          ValidationErrorCode.WORKFLOW_MISSING_NODES,
          "Workflow nodes array is invalid or undefined"
        )
      ],
      warnings: [],
      infos: []
    };
  }

  // Log the nodes array for debugging
  console.log(`[${timestamp}] [validateWorkflow] Nodes array:`, nodes.map(node => ({
    id: node.id,
    type: node.type,
    dataType: node.data?.type,
    originalType: node.data?.originalType,
    label: node.data?.label
  })));

  // Check if edges array is valid
  if (!edges || !Array.isArray(edges)) {
    console.error(`[${timestamp}] [validateWorkflow] Edges array is invalid or undefined`);
    return {
      isValid: false,
      errors: [
        createValidationError(
          ValidationErrorCode.WORKFLOW_MISSING_EDGES,
          "Workflow edges array is invalid or undefined"
        )
      ],
      warnings: [],
      infos: []
    };
  }

  // Check if workflow is empty
  if (nodes.length === 0) {
    console.warn(`[${timestamp}] [validateWorkflow] Workflow is empty (no nodes)`);
    return {
      isValid: false,
      errors: [
        createValidationError(
          ValidationErrorCode.WORKFLOW_EMPTY,
          "Workflow is empty. Please add at least a Start node."
        )
      ],
      warnings: [],
      infos: []
    };
  }

  // Log node types for debugging
  console.log(`[${timestamp}] [validateWorkflow] Node types in workflow:`,
    nodes.map(node => ({
      id: node.id,
      type: node.type,
      dataType: node.data?.type,
      originalType: node.data?.originalType,
      definitionName: node.data?.definition?.name,
      label: node.data?.label
    }))
  );

  // Step 1: Validate nodes structure
  console.log(`[${timestamp}] [validateWorkflow] Step 1: Validating node structure`);
  const nodeValidation = validateNodes(nodes);

  // Step 2: Validate edges structure
  console.log(`[${timestamp}] [validateWorkflow] Step 2: Validating edge structure`);
  const edgeValidation = validateEdges(edges, nodes);

  // Combine validation results
  const errors = [...nodeValidation.errors, ...edgeValidation.errors];
  const warnings = [...nodeValidation.warnings, ...edgeValidation.warnings];
  const infos = [...nodeValidation.infos, ...edgeValidation.infos];

  // If there are structural errors, return early
  if (errors.length > 0) {
    console.warn(`[${timestamp}] [validateWorkflow] Found ${errors.length} structural errors, returning early`);
    return {
      isValid: false,
      errors,
      warnings,
      infos,
    };
  }

  // Step 3: Validate StartNode presence
  console.log(`[${timestamp}] [validateWorkflow] Step 3: Validating StartNode presence`);
  const startNodeValidation = validateStartNode(nodes);
  errors.push(...startNodeValidation.errors);
  warnings.push(...startNodeValidation.warnings);
  infos.push(...startNodeValidation.infos);

  // If there's no StartNode, return early
  if (!startNodeValidation.isValid) {
    console.warn(`[${timestamp}] [validateWorkflow] No StartNode found, validation failed`);
    return {
      isValid: false,
      errors,
      warnings,
      infos,
    };
  }

  console.log(`[${timestamp}] [validateWorkflow] Found StartNode with ID: ${startNodeValidation.startNodeId}`);

  // Step 4: Validate connectivity if requested
  let connectedNodes: Set<string> | undefined;
  if (options.validateConnectivity) {
    console.log(`[${timestamp}] [validateWorkflow] Step 4: Validating node connectivity from StartNode`);
    const connectivityValidation = validateConnectivity(
      nodes,
      edges,
      startNodeValidation.startNodeId!
    );
    errors.push(...connectivityValidation.errors);
    warnings.push(...connectivityValidation.warnings);
    infos.push(...connectivityValidation.infos);
    connectedNodes = connectivityValidation.connectedNodes;

    if (connectedNodes) {
      console.log(`[${timestamp}] [validateWorkflow] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);
    } else {
      console.warn(`[${timestamp}] [validateWorkflow] No connected nodes found from StartNode`);
    }
  } else {
    console.log(`[${timestamp}] [validateWorkflow] Skipping connectivity validation (disabled in options)`);
  }

  // Step 5: Detect cycles if requested
  if (options.validateCycles) {
    console.log(`[${timestamp}] [validateWorkflow] Step 5: Detecting cycles in workflow`);
    const cycleValidation = detectCyclesInWorkflow(nodes, edges);
    errors.push(...cycleValidation.errors);
    warnings.push(...cycleValidation.warnings);
    infos.push(...cycleValidation.infos);

    if (cycleValidation.warnings.length > 0) {
      console.warn(`[${timestamp}] [validateWorkflow] Found cycles in workflow`);
    } else {
      console.log(`[${timestamp}] [validateWorkflow] No cycles detected in workflow`);
    }
  } else {
    console.log(`[${timestamp}] [validateWorkflow] Skipping cycle detection (disabled in options)`);
  }

  // Step 6: Collect missing required fields if requested
  let missingFields: MissingField[] = [];
  if (options.collectMissingFields && connectedNodes) {
    console.log(`[${timestamp}] [validateWorkflow] Step 6: Collecting missing required fields from ${connectedNodes.size} connected nodes`);
    // Pass the edges to the collectMissingRequiredFields function to check if handle inputs are connected
    missingFields = collectMissingRequiredFields(nodes, connectedNodes, edges);

    if (missingFields.length > 0) {
      console.log(`[${timestamp}] [validateWorkflow] Found ${missingFields.length} missing required fields`);
      // Log details of missing fields for debugging
      missingFields.forEach((field, index) => {
        console.log(`[${timestamp}] [validateWorkflow] Missing field ${index + 1}: Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`);
      });
    } else {
      console.log(`[${timestamp}] [validateWorkflow] No missing required fields found`);
    }
  } else {
    console.log(`[${timestamp}] [validateWorkflow] Skipping missing fields collection (disabled in options or no connected nodes)`);
  }

  // Final validation result
  const isValid = errors.length === 0;
  console.log(`[${timestamp}] [validateWorkflow] Validation complete. Result: ${isValid ? "VALID" : "INVALID"}`);
  console.log(`[${timestamp}] [validateWorkflow] Errors: ${errors.length}, Warnings: ${warnings.length}, Missing Fields: ${missingFields.length}`);

  return {
    isValid,
    errors,
    warnings,
    infos,
    missingFields,
    startNodeId: startNodeValidation.startNodeId,
    connectedNodes,
  };
}

/**
 * Main export function for validating a workflow
 *
 * @param workflowData The workflow data to validate
 * @param options Validation options
 * @returns A validation result
 */
export default function validate(
  workflowData: any,
  options?: WorkflowValidationOptions
): ValidationResult {
  // First validate the basic structure
  const structureValidation = validateWorkflowStructure(workflowData);

  if (!structureValidation.isValid) {
    return structureValidation;
  }

  // Extract nodes and edges
  const nodes = workflowData.nodes || [];
  const edges = workflowData.edges || [];

  // Validate the workflow
  return validateWorkflow(nodes, edges, options);
}
