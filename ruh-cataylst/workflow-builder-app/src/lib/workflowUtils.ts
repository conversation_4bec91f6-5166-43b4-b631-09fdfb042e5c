// src/lib/workflowUtils.ts
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types"; // Adjust path if needed
import { migrateApprovalFlagToDefinition } from "./approvalMigration";

/**
 * Interface for validation result
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
  data?: any;
}

/**
 * Formats the current workflow state (nodes and edges) into a savable JSON structure
 * and triggers a file download.
 *
 * @param nodes - The current array of nodes from React Flow.
 * @param edges - The current array of edges from React Flow.
 * @param filename - Optional filename for the downloaded file (defaults to 'workflow.json').
 * @param workflowName - Optional name for the workflow (defaults to 'Untitled Workflow').
 */
export function saveWorkflowToFile(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  filename: string = "workflow.json",
  workflowName: string = "Untitled Workflow",
): void {
  // Map nodes to desired structure - preserve all node data for proper workflow restoration
  const mappedNodes = nodes.map((node) => {
    // Create a clean copy of the node with all its properties
    return {
      id: node.id,
      type: node.type,
      position: node.position,
      data: node.data,
    };
  });

  // Map edges - preserve all edge data for proper workflow restoration
  const mappedEdges = edges.map((edge) => {
    return {
      id: edge.id,
      source: edge.source,
      sourceHandle: edge.sourceHandle,
      target: edge.target,
      targetHandle: edge.targetHandle,
    };
  });

  // Create a simple JSON structure with workflow name, nodes and edges as the main keys
  const workflowToSave = {
    workflow_name: workflowName,
    nodes: mappedNodes,
    edges: mappedEdges,
  };

  try {
    const jsonString = JSON.stringify(workflowToSave, null, 2); // Pretty print
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename; // Use the provided filename
    document.body.appendChild(link); // Required for Firefox
    link.click();
    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    console.log(`Workflow saved successfully as ${filename}.`);
  } catch (error) {
    console.error("Failed to save workflow:", error);
  }
}

/**
 * Validates a workflow JSON structure to ensure it has the required fields and format.
 *
 * @param workflowData - The workflow data to validate.
 * @returns A ValidationResult object indicating if the data is valid and any error messages.
 */
export function validateWorkflowJson(workflowData: any): ValidationResult {
  // Check if the data is an object
  if (!workflowData || typeof workflowData !== "object") {
    return {
      isValid: false,
      error: "Workflow data must be a valid JSON object",
    };
  }

  // Check workflow_name if present (not required)
  if (workflowData.workflow_name && typeof workflowData.workflow_name !== "string") {
    return {
      isValid: false,
      error: "Workflow name must be a string",
    };
  }

  // Check for required top-level properties
  if (!Array.isArray(workflowData.nodes)) {
    return {
      isValid: false,
      error: 'Workflow must contain a "nodes" array',
    };
  }

  // Check for edges array (previously called connections)
  if (!Array.isArray(workflowData.edges) && !Array.isArray(workflowData.connections)) {
    return {
      isValid: false,
      error: 'Workflow must contain an "edges" array',
    };
  }

  // For backward compatibility, use edges if available, otherwise use connections
  const edgesArray = workflowData.edges || workflowData.connections;

  // Validate each node
  for (let i = 0; i < workflowData.nodes.length; i++) {
    const node = workflowData.nodes[i];

    // Check for required node properties
    if (!node.id) {
      return {
        isValid: false,
        error: `Node at index ${i} is missing an "id" property`,
      };
    }

    if (!node.type) {
      return {
        isValid: false,
        error: `Node at index ${i} is missing a "type" property`,
      };
    }

    if (
      !node.position ||
      typeof node.position !== "object" ||
      typeof node.position.x !== "number" ||
      typeof node.position.y !== "number"
    ) {
      return {
        isValid: false,
        error: `Node at index ${i} has an invalid "position" property`,
      };
    }

    if (!node.data || typeof node.data !== "object") {
      return {
        isValid: false,
        error: `Node at index ${i} is missing a valid "data" property`,
      };
    }

    // Check for required data properties
    if (!node.data.type) {
      return {
        isValid: false,
        error: `Node at index ${i} is missing a "data.type" property`,
      };
    }

    if (!node.data.label) {
      return {
        isValid: false,
        error: `Node at index ${i} is missing a "data.label" property`,
      };
    }

    if (!node.data.definition || typeof node.data.definition !== "object") {
      return {
        isValid: false,
        error: `Node at index ${i} is missing a valid "data.definition" property`,
      };
    }
  }

  // Validate each edge (previously called connection)
  for (let i = 0; i < edgesArray.length; i++) {
    const edge = edgesArray[i];

    // Check for required edge properties
    if (!edge.id) {
      return {
        isValid: false,
        error: `Edge at index ${i} is missing an "id" property`,
      };
    }

    if (!edge.source) {
      return {
        isValid: false,
        error: `Edge at index ${i} is missing a "source" property`,
      };
    }

    if (!edge.target) {
      return {
        isValid: false,
        error: `Edge at index ${i} is missing a "target" property`,
      };
    }

    // Check that source and target nodes exist
    const sourceExists = workflowData.nodes.some((node: any) => node.id === edge.source);
    if (!sourceExists) {
      return {
        isValid: false,
        error: `Edge at index ${i} references a non-existent source node: ${edge.source}`,
      };
    }

    const targetExists = workflowData.nodes.some((node: any) => node.id === edge.target);
    if (!targetExists) {
      return {
        isValid: false,
        error: `Edge at index ${i} references a non-existent target node: ${edge.target}`,
      };
    }
  }

  // If we've made it this far, the workflow is valid
  return {
    isValid: true,
    data: workflowData,
  };
}

/**
 * Converts a validated workflow JSON structure to ReactFlow format.
 *
 * @param workflowData - The validated workflow data.
 * @returns An object containing nodes and edges arrays for ReactFlow.
 */
export function convertWorkflowJsonToReactFlow(workflowData: any): {
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];
} {
  console.log("Converting workflow data to ReactFlow format:", workflowData);

  // Check if we're dealing with a wrapped workflow structure
  // This happens when loading from the builder URL where the data is in workflow_data
  if (workflowData.workflow_data && workflowData.workflow_data.nodes) {
    console.log("Detected wrapped workflow structure, unwrapping...");
    workflowData = workflowData.workflow_data;
  }

  // FEATURE: Combine connected and unconnected nodes for complete canvas restoration
  const allNodesToProcess = [...workflowData.nodes];
  
  // Add unconnected nodes if they exist
  if (workflowData.unconnected_nodes && Array.isArray(workflowData.unconnected_nodes)) {
    console.log(`Found ${workflowData.unconnected_nodes.length} unconnected nodes to restore`);
    console.log("Unconnected nodes:", workflowData.unconnected_nodes.map((n: any) => `${n.data?.label || 'Unknown'} (${n.id})`).join(", "));
    allNodesToProcess.push(...workflowData.unconnected_nodes);
  }

  console.log(`Total nodes to process: ${allNodesToProcess.length} (${workflowData.nodes.length} connected + ${workflowData.unconnected_nodes?.length || 0} unconnected)`);

  // Map nodes directly - they should already be in the correct format
  const nodes: Node<WorkflowNodeData>[] = allNodesToProcess.map((node: any) => {
    console.log("Processing node:", node);

    // Special handling for StartNode to ensure its configuration is preserved
    if (node.data && node.data.originalType === "StartNode") {
      console.log("Found StartNode, ensuring config is preserved:", node);

      // Ensure the node has a config object
      if (!node.data.config) {
        node.data.config = {};
      }

      // Ensure the config has a collected_parameters object
      if (!node.data.config.collected_parameters) {
        node.data.config.collected_parameters = {};
      }

      // Ensure all parameters in collected_parameters have the required property set
      // This is critical for pre-built workflows where the required property might not be set
      if (node.data.config.collected_parameters) {
        Object.keys(node.data.config.collected_parameters).forEach((paramId) => {
          const param = node.data.config.collected_parameters[paramId];
          // If required is undefined, set it to true (consider required unless explicitly false)
          if (param.required === undefined) {
            console.log(`Setting required=true for parameter ${paramId} in StartNode`);
            param.required = true;
          }
        });
      }

      console.log("StartNode config after ensuring structure:", node.data.config);
    }

    return {
      id: node.id,
      type: node.type || "WorkflowNode", // Ensure we have a type, default to WorkflowNode
      position: node.position,
      data: node.data as WorkflowNodeData,
      // Include any other properties that might be needed
      width: node.width,
      height: node.height,
      selected: false, // Reset selection state
      dragging: false, // Reset dragging state
    };
  });

  // Map edges (previously called connections)
  // For backward compatibility, use edges if available, otherwise use connections
  const edgesArray = workflowData.edges || workflowData.connections;

  const edges: Edge[] = edgesArray.map((edge: any) => {
    console.log("Processing edge:", edge);
    return {
      id: edge.id,
      source: edge.source,
      sourceHandle: edge.sourceHandle,
      target: edge.target,
      targetHandle: edge.targetHandle,
      type: edge.type || "default", // Use custom edge type
      // Include any other properties that might be needed
      animated: true, // Add animation to edges
    };
  });

  console.log("Converted nodes:", nodes);
  console.log("Converted edges:", edges);

  // Migrate approval flags from config to definition
  const migratedNodes = migrateApprovalFlagToDefinition(nodes);
  console.log("Migrated nodes:", migratedNodes);

  return { nodes: migratedNodes, edges };
}

/**
 * Loads a workflow from JSON data, validates it, and returns it in ReactFlow format.
 *
 * @param workflowData - The workflow data to load.
 * @returns A ValidationResult containing the converted workflow data if valid.
 */
export function loadWorkflowFromJson(workflowData: any): ValidationResult {
  // First validate the workflow data
  const validationResult = validateWorkflowJson(workflowData);

  if (!validationResult.isValid) {
    return validationResult;
  }

  try {
    // Convert the validated data to ReactFlow format
    const reactFlowData = convertWorkflowJsonToReactFlow(workflowData);

    return {
      isValid: true,
      data: reactFlowData,
    };
  } catch (error) {
    return {
      isValid: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to convert workflow data to ReactFlow format",
    };
  }
}
