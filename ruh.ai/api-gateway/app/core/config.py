from typing import Any, Dict, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "api-gateway"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"

    # Service endpoints
    USER_SERVICE_HOST: str = "user_service"
    USER_SERVICE_PORT: int = 50052
    ADMIN_SERVICE_HOST: str = "admin_service"
    ADMIN_SERVICE_PORT: int = 50053
    NOTIFICATION_SERVICE_HOST: str = "notification_service"
    NOTIFICATION_SERVICE_PORT: int = 50060
    COMMUNICATION_SERVICE_HOST: str = "communication_service"
    COMMUNICATION_SERVICE_PORT: int = 50055
    WORKFLOW_SERVICE_HOST: str = "localhost"
    WORKFLOW_SERVICE_PORT: int = 50056
    AGENT_SERVICE_HOST: str = "localhost"
    AGENT_SERVICE_PORT: int = 50057
    MCP_SERVICE_HOST: str = "localhost"
    MCP_SERVICE_PORT: int = 50058
    ORGANISATION_SERVICE_HOST: str = "localhost"
    ORGANISATION_SERVICE_PORT: int = 50070
    AUTH_SERVICE_HOST: str = "localhost"
    AUTH_SERVICE_PORT: int = 50054
    ANALYTICS_SERVICE_HOST: str = "localhost"
    ANALYTICS_SERVICE_PORT: int = 50059

    # Kafka settings
    KAFKA_BROKER_HOST: str = "localhost"
    KAFKA_BROKER_PORT: str = "9092"

    # Redis settings
    REDIS_HOST: str = ""
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str = ""
    REDIS_JWT_ACCESS_EXPIRE_SEC: int = 3600
    REDIS_URI: Optional[str] = None  # Add this line

    DEPLOYMENT_WORKER_AUTH_KEY: str = Field(..., env="DEPLOYMENT_WORKER_AUTH_KEY")
    ORGANISATION_SERVICE_AUTH_KEY: str = Field(..., env="ORGANISATION_SERVICE_AUTH_KEY")
    CORS_METHODS: str = Field(default='["*"]', env="CORS_METHODS")
    CORS_HEADERS: str = Field(default='["*"]', env="CORS_HEADERS")

    @validator("REDIS_URI", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"REDIS_HOST", "REDIS_PORT", "REDIS_DB"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required Redis configuration: {missing}")

        auth_part = f":{values.get('REDIS_PASSWORD')}@" if values.get("REDIS_PASSWORD") else ""
        return f"redis://{auth_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT', 6379)}/{values.get('REDIS_DB', 0)}"

    # Rate limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60  # seconds

    # JWT settings
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # CORS settings
    CORS_ORIGINS: str = "http://localhost,http://localhost:3000,http://localhost:8000"
    CORS_CREDENTIALS: bool = True
    # CORS_METHODS: list[str] = ["*"]
    # CORS_HEADERS: list[str] = ["*"]
    FRONTEND_AUTH_URL: str = ""
    COOKIE_DOMAIN: str = ""

    # Service discovery settings
    SERVICE_DISCOVERY_ENABLED: bool = False
    SERVICE_DISCOVERY_HOST: str = "consul"
    SERVICE_DISCOVERY_PORT: int = 8500

    REPO_URL: str = ""
    GIT_TOKEN: str = ""

    # Service connection settings
    SERVICE_CONNECTIONS: Dict[str, Dict[str, str]] = {
        "user_service": {"host": "localhost", "port": "50052"},
        "admin_service": {"host": "admin_service", "port": "50053"},
        "notification_service": {"host": "notification_service", "port": "50060"},
        "workflow_service": {"host": "localhost", "port": "50056"},
        "agent_service": {"host": "localhost", "port": "50057"},
    }

    # OAuth Provider Configurations
    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_REDIRECT_URI: str = ""

    # Microsoft OAuth
    MICROSOFT_CLIENT_ID: str = ""
    MICROSOFT_CLIENT_SECRET: str = ""
    MICROSOFT_REDIRECT_URI: str = ""

    # GitHub OAuth
    GITHUB_CLIENT_ID: str = ""
    GITHUB_CLIENT_SECRET: str = ""
    GITHUB_REDIRECT_URI: str = ""

    # Generic OAuth (for custom providers)
    CUSTOM_OAUTH_PROVIDERS: str = "{}"  # JSON string of custom provider configs

    GOOGLE_APPLICATION_CREDENTIALS: str = ""
    GOOGLE_PROJECT_ID: str = ""

    # Auth Key for get_workflow_by_id (for orchestration only)
    ORCHESTRATION_SERVER_AUTH_KEY: str
    AGENT_PLATFORM_AUTH_KEY: str
    WORKFLOW_SERVICE_AUTH_KEY: str
    DEPLOYMENT_WORKER_AUTH_KEY: str
    ORGANISATION_SERVICE_AUTH_KEY: str

    # Auth Key for Authentication Service
    AUTH_SERVICE_SERVER_AUTH_KEY: str

    GCS_CRED: str
    BUCKET_NAME: str

    # Livekit settings
    LIVEKIT_URL: str = ""
    LIVEKIT_API_KEY: str = ""
    LIVEKIT_API_SECRET: str = ""

    # OpenAI settings
    OPENAI_API_KEY: str = ""
    OPENAI_MODEL: str = "gpt-4o"
    
    # Requesty settings
    REQUESTY_API_KEY: str = ""
    REQUESTY_BASE_URL: str = "https://router.requesty.ai/v1"

    CLIENT_ID: str = ""
    CLIENT_SECRET: str = ""

    REDIRECT_URI: str = ""
    REDIRECT_URI_AFTER_GITHUB_OAUTH: str = ""

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
