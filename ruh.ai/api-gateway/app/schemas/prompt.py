from typing import Dict, Any, Optional, Literal
from pydantic import BaseModel, Field


class PromptImprovementRequest(BaseModel):
    """Request model for prompt improvement."""

    original_prompt: str = Field(..., description="The original system prompt to improve")
    agent_context: Optional[Dict[str, Any]] = Field(
        None, description="Optional context about the agent (capabilities, purpose, etc.)"
    )


class PromptImprovementResponse(BaseModel):
    """Response model for prompt improvement."""

    original_prompt: str = Field(..., description="The original system prompt")
    improved_prompt: str = Field(..., description="The improved system prompt")


class UserPromptImprovementRequest(BaseModel):
    """Request model for user prompt improvement."""

    original_prompt: str = Field(..., description="The original user prompt to improve")
    mode: Literal["ACT", "ASK"] = Field(..., description="The mode of the prompt: 'ACT' for task/action prompts, 'ASK' for question/search prompts")


class UserPromptImprovementResponse(BaseModel):
    """Response model for user prompt improvement."""

    original_prompt: str = Field(..., description="The original user prompt")
    improved_prompt: str = Field(..., description="The improved user prompt")
    mode: str = Field(..., description="The mode used for improvement")
